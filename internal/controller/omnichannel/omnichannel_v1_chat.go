package omnichannel

import (
	v1 "brainHub/api/omnichannel/v1"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"brainHub/internal/model/omnichannel"
	"brainHub/internal/service"
	"brainHub/utility"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/text/gstr"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

func (c *ControllerV1) Chat(ctx context.Context, req *v1.ChatReq) (res *v1.ChatRes, err error) {

	res = &v1.ChatRes{
		BaseRes: v1.BaseRes{
			Code:    consts.Success.Code(),
			Message: consts.Success.Message(),
		},

		Hit:       true,
		Sensitive: false,
	}
	r := g.RequestFromCtx(ctx)
	fnCreateDefaultFailedAnswer := func(code gcode.Code) string {
		ret := ""
		ans, _ := service.OmniChannel().CreateAnswer(ctx, g.MapStrStr{
			code.Message(): "",
		})

		if len(ans) > 0 {
			res.Answer = gjson.New(ans).MustToJsonString()
			ret = res.Answer

		}

		return ret

	}
	fnCreateTextAnswer := func(text string) string {
		ans, _ := service.OmniChannel().CreateAnswer(ctx, g.MapStrStr{
			text: "",
		})

		if len(ans) > 0 {
			res.Answer = gjson.New(ans).MustToJsonString()
		}

		return res.Answer
	}

	userMessage := utility.ConvertAnyToGenericMessage(req)

	var resp = &llm.ResponseData{
		TenantID:  req.TenantID,
		UserID:    req.UserId,
		ServiceID: req.ServiceId,
		Channel:   req.Channel,
	}

	ai, err := service.AiRouter().Select(ctx, &model.AiSelectorInput{
		TenantID:  req.TenantID,
		ServiceID: req.ServiceId,
		UserID:    req.UserId,
		Channel:   req.Channel,
	})

	if err != nil {
		if gerror.Code(err) == consts.CrawlWebsiteIsNotFinished {
			resp.Response = fnCreateDefaultFailedAnswer(consts.CrawlWebsiteIsNotFinished)

		} else {

			resp.Response = fnCreateDefaultFailedAnswer(consts.OmniChannelChatFailed)
		}

	} else {
		cost := gtime.FuncCost(func() {
			// 特殊密語情況下的判斷
			key := "system.encryption_setting."
			vPrefix, _ := g.Cfg().Get(ctx, key+"prefix")
			if !vPrefix.IsEmpty() &&
				gstr.HasPrefix(req.Question, vPrefix.String()) {
				vSettingUrl, _ := g.Cfg().Get(ctx, key+"setting_url")
				vSystemInstructionUrl, _ := g.Cfg().Get(ctx, key+"system_instruction")
				if !vSettingUrl.IsEmpty() && !vSystemInstructionUrl.IsEmpty() {
					// 從配置文件中獲得設定的模板
					vTemplate, _ := g.Cfg().Get(ctx, key+"template")
					if !vTemplate.IsEmpty() {
						if gjson.Valid(vTemplate.String()) {

							jsTemplate := gjson.New(vTemplate.String())
							carouselInput := &omnichannel.CarouselInput{
								Text: jsTemplate.Get("text").String(),
								Elements: []*omnichannel.CarouselElement{
									{
										Title:    jsTemplate.Get("title").String(),
										Subtitle: jsTemplate.Get("subTitle").String(),
										ImageUrl: jsTemplate.Get("imageUrl").String(),
										Actions: []*omnichannel.CarouselAction{
											{
												Type:  "Url",
												Title: jsTemplate.Get("title_setting").String(),
												Url:   fmt.Sprintf("%s?TenantID=%s&ServiceID=%s&UserID=%s", vSettingUrl.String(), req.TenantID, req.ServiceId, req.UserId),
											},
											{
												Type:  "Url",
												Title: jsTemplate.Get("title_system_instruction").String(),
												Url:   fmt.Sprintf("%s?TenantID=%s", vSystemInstructionUrl.String(), req.TenantID),
											},
										},
									},
								},
							}
							ans, e := service.OmniChannel().CreateAnswer(ctx, carouselInput)
							if e != nil {
								g.Log().Error(ctx, e)
							} else {
								if len(ans) > 0 {
									res.Answer = gjson.New(ans).MustToJsonString()
									resp.Response = res.Answer

								}
							}

						} else {
							g.Log().Warning(ctx, "template is not valid json format")
						}
					} else {
						g.Log().Warning(ctx, "no template found in config file")
					}

				} else {
					g.Log().Warning(ctx, "no setting url or system instruction url found in config file")
				}

			}
			// 正常狀況下的文字聊天
			if g.IsEmpty(resp.Response) {

				resp, err = ai.Chat(ctx, &llm.Message{
					Content:     req.Question,
					ContentType: consts.ContentTypeText,
				})
			}

		})

		// 記錄執行時間
		g.Log().Noticef(ctx, "Chat cost: %v", cost.String())

		if err != nil {
			// 確保 resp 不為 nil，避免 nil pointer dereference
			if resp == nil {
				resp = &llm.ResponseData{
					TenantID:  req.TenantID,
					UserID:    req.UserId,
					ServiceID: req.ServiceId,
					Channel:   req.Channel,
				}
			}
			resp.Response = fnCreateDefaultFailedAnswer(consts.OmniChannelChatFailed)
		} else {
			// 確保 resp 不為 nil，避免 nil pointer dereference
			if resp == nil {
				resp = &llm.ResponseData{
					TenantID:  req.TenantID,
					UserID:    req.UserId,
					ServiceID: req.ServiceId,
					Channel:   req.Channel,
					Response:  "",
				}
			}

			// ai 直接生成指定 json 格式數據
			strResult := utility.ParseJsonFormatResponse(gconv.String(resp.Response))

			resp.TenantID = req.TenantID
			resp.UserID = req.UserId
			resp.ServiceID = req.ServiceId
			resp.Channel = req.Channel
			resp.Response = strResult

			if !gjson.Valid(strResult) {
				g.Log().Noticef(ctx, "Invalid json format response: %v ", strResult)
				resp.Response = fnCreateTextAnswer(strResult)
			} else {
				res.Answer = strResult
			}

		}

	}

	// 最終確保 resp 不為 nil，避免後續處理中的 nil pointer dereference
	if resp == nil {
		resp = &llm.ResponseData{
			TenantID:  req.TenantID,
			UserID:    req.UserId,
			ServiceID: req.ServiceId,
			Channel:   req.Channel,
			Response:  fnCreateDefaultFailedAnswer(consts.OmniChannelChatFailed),
		}
	}

	aiMessage := utility.ConvertAnyToGenericMessage(resp)

	if userMessage != nil {
		_ = service.DSH().InsertNewChatMessage(ctx, userMessage)
	}
	if aiMessage != nil {

		_ = service.DSH().InsertNewChatMessage(ctx, aiMessage)
	}

	r.Response.WriteJsonExit(res)

	return
}
