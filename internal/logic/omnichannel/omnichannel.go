package omnichannel

import (
	"brainHub/internal/consts"
	"brainHub/internal/model/omnichannel"
	"brainHub/internal/service"
	"context"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
)

func init() {
	service.RegisterOmniChannel(New())
}

type sOmniChannel struct {
}

func New() service.IOmniChannel {
	return &sOmniChannel{}
}
func (s *sOmniChannel) logger() glog.ILogger {

	return g.Log().Cat(consts.CatalogOmniChannel)
}

func (s *sOmniChannel) CreateAnswer(ctx context.Context, in g.MapStrStr) (answer []*omnichannel.Root, err error) {
	s.logger().Infof(ctx, "create answer by : %v", gjson.New(in).MustToJsonIndentString())
	if in == nil {
		err = gerror.New("input answer is nil")
		s.logger().Error(ctx, err)
		return
	}
	answer = make([]*omnichannel.Root, 0)

	mapAnswers := gmap.NewStrStrMapFrom(in)
	answerText := gstr.Join(mapAnswers.Keys(), consts.AnswerSep)
	// 每一個 value ： <ele ^^ ele>
	quickReply := gstr.SplitAndTrim(gstr.Join(mapAnswers.Values(), consts.QRSep), consts.QRSep)

	var ans = &omnichannel.Root{
		Text: answerText,
		Type: "Text",
	}

	fnCreateItem := func(qrStr string) *omnichannel.Item {
		return &omnichannel.Item{
			Action: &omnichannel.Action{
				Data:        qrStr,
				DisplayText: qrStr,
				Title:       qrStr,
				Type:        "Postback",
			},
			Type: "Action",
		}
	}

	if len(quickReply) > 0 {
		var items = make([]*omnichannel.Item, 0)
		for _, qrStr := range quickReply {
			items = append(items, fnCreateItem(qrStr))
		}

		ans.QuickReply = &omnichannel.QuickReply{
			Items: items,
		}
	}
	answer = append(answer, ans)

	return

}

func (s *sOmniChannel) CreateCarousel(ctx context.Context) {

}
